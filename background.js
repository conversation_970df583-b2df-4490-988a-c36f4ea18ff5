chrome.runtime.onInstalled.addListener(() => {
  console.log("Proxy Switcher installed");
});

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "setProxy") {
    const config = {
      mode: "fixed_servers",
      rules: {
        singleProxy: {
          scheme: message.scheme,
          host: message.host,
          port: parseInt(message.port)
        },
        bypassList: ["localhost"]
      }
    };
    chrome.proxy.settings.set({ value: config, scope: "regular" }, () => {
      sendResponse({ status: "Proxy set" });
    });
    return true; // Keep the message channel open for sendResponse
  }

  if (message.action === "clearProxy") {
    chrome.proxy.settings.clear({}, () => {
      sendResponse({ status: "Proxy cleared" });
    });
    return true;
  }
});