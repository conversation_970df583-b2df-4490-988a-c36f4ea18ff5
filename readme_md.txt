# My Proxy Switcher

这是一个简单的 Chrome 插件，模仿 SwitchyOmega，方便一键切换代理。

## 文件结构

```
MyProxySwitcher/
├── manifest.json
├── background.js
├── popup.html
├── popup.js
├── icon16.png
├── icon48.png
├── icon128.png
```

## 使用方法

1. 将上述文件放入同一个文件夹。
2. 打开 Chrome 扩展程序页面，启用「开发者模式」。
3. 点击「加载已解压的扩展程序」，选择该文件夹。
4. 点击插件图标，输入代理信息，点击「Set Proxy」即可设置代理，点击「Clear Proxy」即可清除代理。

## 提示
- `scheme` 可以是 `http` 或 `https`。
- `bypassList` 默认跳过 `localhost`。
- 若需要更多规则，可自行扩展 `background.js`。

祝你使用愉快！
