<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Proxy Switcher</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 8px;
      width: 200px;
      background-color: #ffffff;
      color: #333;
      margin: 0;
      box-sizing: border-box;
    }

    .proxy-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 2px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      font-size: 14px;
    }

    .proxy-item:hover {
      background-color: #f0f0f0;
    }

    .proxy-item.active {
      background-color: #007acc;
      color: white;
    }

    .proxy-item.active:hover {
      background-color: #005a9e;
    }

    .proxy-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      color: white;
      flex-shrink: 0;
    }

    .icon-direct {
      background-color: #28a745;
    }

    .icon-system {
      background-color: #6c757d;
    }

    .icon-custom {
      background-color: #007acc;
    }

    .icon-auto {
      background-color: #17a2b8;
    }

    .proxy-name {
      flex: 1;
      font-weight: 500;
    }

    .proxy-status {
      font-size: 11px;
      opacity: 0.7;
      margin-left: 4px;
    }

    .divider {
      height: 1px;
      background-color: #e0e0e0;
      margin: 4px 0;
    }

    .settings-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      font-size: 14px;
      color: #666;
    }

    .settings-item:hover {
      background-color: #f0f0f0;
    }

    .settings-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      opacity: 0.6;
    }

    .custom-preset {
      background-color: #f8f9fa;
      border-left: 3px solid #007acc;
    }

    .custom-preset.active {
      background-color: #007acc;
      border-left-color: #ffffff;
    }
  </style>
</head>
<body>
  <div class="proxy-item" data-preset="direct" id="direct-item">
    <div class="proxy-icon icon-direct">🌐</div>
    <div class="proxy-name">直接连接</div>
    <div class="proxy-status" id="direct-status"></div>
  </div>

  <div class="proxy-item" data-preset="system" id="system-item">
    <div class="proxy-icon icon-system">⚙️</div>
    <div class="proxy-name">系统代理</div>
    <div class="proxy-status" id="system-status"></div>
  </div>

  <div class="proxy-item" data-preset="auto" id="auto-item">
    <div class="proxy-icon icon-auto">🔄</div>
    <div class="proxy-name">(外部情景模式)</div>
    <div class="proxy-status">auto switch</div>
  </div>

  <div id="custom-presets">
    <!-- 自定义预设将在这里动态加载 -->
  </div>

  <div class="divider"></div>

  <div class="settings-item" id="options">
    <div class="settings-icon">🔧</div>
    <div>选项</div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
