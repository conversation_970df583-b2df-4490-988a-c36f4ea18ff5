<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Proxy Settings</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 15px;
      width: 280px;
      background-color: #f4f7f6;
      color: #333;
      margin: 0;
      box-sizing: border-box;
    }

    h4 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 20px;
      font-size: 1.2em;
    }

    .preset-section {
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ddd;
    }

    .preset-section label {
      display: block;
      margin-bottom: 5px;
      font-weight: 600;
      color: #555;
      font-size: 0.9em;
    }

    select {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      box-sizing: border-box;
      font-size: 0.9em;
      background-color: white;
      transition: border-color 0.3s ease;
    }

    select:focus {
      border-color: #007bff;
      outline: none;
      box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
    }

    .status-section {
      margin-bottom: 15px;
      padding: 12px;
      background-color: #fff;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }

    .current-status {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .status-label, .rule-label {
      font-weight: 600;
      color: #555;
      font-size: 0.85em;
    }

    .status-value {
      font-weight: 500;
      color: #2c3e50;
      font-size: 0.9em;
    }

    .active-rule {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;
    }

    .rule-value {
      font-size: 0.8em;
      color: #28a745;
      font-weight: 500;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .actions-section {
      margin-top: 15px;
    }

    button {
      width: 100%;
      padding: 10px 15px;
      margin-bottom: 8px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1em;
      font-weight: bold;
      transition: background-color 0.3s ease, transform 0.2s ease;
    }

    button#clear {
      background-color: #28a745;
      color: white;
    }

    button#clear:hover {
      background-color: #218838;
      transform: translateY(-1px);
    }

    button#options {
      background-color: #6c757d;
      color: white;
      font-size: 0.9em;
    }

    button#options:hover {
      background-color: #5a6268;
      transform: translateY(-1px);
    }



    button:active {
      transform: translateY(0);
    }

    .status {
      text-align: center;
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 0.85em;
      display: none;
    }

    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  </style>
</head>
<body>
  <h4>Betterfly Proxy Switcher</h4>

  <!-- Current Status -->
  <div class="status-section">
    <div class="current-status">
      <span class="status-label">Current:</span>
      <span id="current-proxy" class="status-value">Loading...</span>
    </div>
    <div class="active-rule" id="active-rule" style="display: none;">
      <span class="rule-label">Auto Rule:</span>
      <span id="active-rule-text" class="rule-value"></span>
    </div>
  </div>

  <!-- Quick Preset Selection -->
  <div class="preset-section">
    <label for="presets">Switch to:</label>
    <select id="presets">
      <option value="">Select preset...</option>
      <option value="direct">Direct Connection</option>
    </select>
  </div>

  <!-- Quick Actions -->
  <div class="actions-section">
    <button type="button" id="clear">Direct Connection</button>
    <button type="button" id="options">Settings & Management</button>
  </div>

  <div id="status" class="status"></div>

  <script src="popup.js"></script>
</body>
</html>
