<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Proxy Settings</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 15px;
      width: 280px;
      background-color: #f4f7f6;
      color: #333;
      margin: 0;
      box-sizing: border-box;
    }

    h4 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 15px;
      font-size: 1.2em;
    }

    .status-section {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 5px;
      border-left: 4px solid #007bff;
    }

    .current-status {
      display: flex;
      align-items: center;
      font-size: 0.9em;
    }

    .status-label {
      font-weight: 600;
      color: #555;
      margin-right: 8px;
    }

    .status-value {
      color: #007bff;
      font-weight: 500;
      flex: 1;
    }

    .status-value.direct {
      color: #28a745;
    }

    .status-value.proxy {
      color: #007bff;
    }

    .status-value.system {
      color: #6c757d;
    }

    .status-value.error {
      color: #dc3545;
    }

    .preset-section {
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ddd;
    }

    .preset-section label {
      display: block;
      margin-bottom: 5px;
      font-weight: 600;
      color: #555;
      font-size: 0.9em;
    }

    select {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      box-sizing: border-box;
      font-size: 0.9em;
      background-color: white;
      transition: border-color 0.3s ease;
    }

    select:focus {
      border-color: #007bff;
      outline: none;
      box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
    }

    .manual-section {
      margin-bottom: 15px;
    }

    .manual-section label {
      display: block;
      margin-bottom: 5px;
      font-weight: 600;
      color: #555;
      font-size: 0.9em;
    }

    input[type="text"],
    input[type="number"] {
      width: calc(100% - 20px);
      padding: 10px;
      margin-bottom: 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
      box-sizing: border-box;
      font-size: 0.9em;
      transition: border-color 0.3s ease;
    }

    input[type="text"]:focus,
    input[type="number"]:focus {
      border-color: #007bff;
      outline: none;
      box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
    }

    button {
      width: 100%;
      padding: 10px 15px;
      margin-bottom: 8px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1em;
      font-weight: bold;
      transition: background-color 0.3s ease, transform 0.2s ease;
    }

    button#set {
      background-color: #5b4aca;
      color: white;
    }

    button#set:hover {
      background-color: #5b4aca;
      transform: translateY(-1px);
    }

    button#clear {
      background-color: #dc3545;
      color: white;
    }

    button#clear:hover {
      background-color: #c82333;
      transform: translateY(-1px);
    }

    button#options {
      background-color: #6c757d;
      color: white;
      font-size: 0.9em;
    }

    button#options:hover {
      background-color: #5a6268;
      transform: translateY(-1px);
    }

    button:active {
      transform: translateY(0);
    }

    .status {
      text-align: center;
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 0.85em;
      display: none;
    }

    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  </style>
</head>
<body>
  <h4>Betterfly Proxy Settings</h4>

  <div class="status-section">
    <div class="current-status">
      <span class="status-label">Current Status:</span>
      <span id="current-proxy-status" class="status-value">Loading...</span>
    </div>
  </div>

  <div class="preset-section">
    <label for="presets">Quick Presets:</label>
    <select id="presets">
      <option value="">Select a preset...</option>
      <option value="direct">Direct Connection</option>
    </select>
  </div>

  <div class="manual-section">
    <label>Manual Configuration:</label>
    <input id="scheme" placeholder="http or https" type="text" />
    <input id="host" placeholder="Proxy Host" type="text" />
    <input id="port" placeholder="Port" type="number" />
  </div>

  <button type="button" id="set">Set Proxy</button>
  <button type="button" id="clear">Clear Proxy</button>
  <button type="button" id="options">More Settings</button>

  <div id="status" class="status"></div>

  <script src="popup.js"></script>
</body>
</html>
