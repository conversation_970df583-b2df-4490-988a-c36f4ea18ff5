<!-- popup.html -->
<!-- <!DOCTYPE html>
<html>
<head>
  <title>Proxy Switcher</title>
  <style>
    body { font-family: Arial; padding: 10px; width: 200px; }
    input { width: 100%; margin-bottom: 8px; }
    button { width: 100%; }
  </style>
</head>
<body>
  <h4>Set Proxy</h4>
  <input id="scheme" placeholder="http or https" />
  <input id="host" placeholder="Proxy Host" />
  <input id="port" placeholder="Port" type="number" />
  <button id="set">Set Proxy</button>
  <button id="clear">Clear Proxy</button>
  <script src="popup.js"></script>
</body>
</html> -->

<!DOCTYPE html>
<html>
<head>
  <title>Proxy Switcher</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 15px;
      width: 250px;
      background-color: #f4f7f6;
      color: #333;
      margin: 0;
      box-sizing: border-box;
    }

    h4 {
      text-align: center;
      color: #2c3e50;
      margin-bottom: 20px;
      font-size: 1.2em;
    }

    input[type="text"],
    input[type="number"] {
      width: calc(100% - 20px); /* Account for padding */
      padding: 10px;
      margin-bottom: 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
      box-sizing: border-box;
      font-size: 0.9em;
      transition: border-color 0.3s ease;
    }

    input[type="text"]:focus,
    input[type="number"]:focus {
      border-color: #007bff;
      outline: none;
      box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
    }

    button {
      width: 100%;
      padding: 10px 15px;
      margin-bottom: 8px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1em;
      font-weight: bold;
      transition: background-color 0.3s ease, transform 0.2s ease;
    }

    button#set {
      background-color: #28a745; /* Green for "Set" */
      color: white;
    }

    button#set:hover {
      background-color: #218838;
      transform: translateY(-1px);
    }

    button#clear {
      background-color: #dc3545; /* Red for "Clear" */
      color: white;
    }

    button#clear:hover {
      background-color: #c82333;
      transform: translateY(-1px);
    }

    button:active {
      transform: translateY(0);
    }
  </style>
</head>
<body>
  <h4>Proxy Settings</h4>
  <input id="scheme" placeholder="http or https" type="text" />
  <input id="host" placeholder="Proxy Host" type="text" />
  <input id="port" placeholder="Port" type="number" />
  <button id="set">Set Proxy</button>
  <button id="clear">Clear Proxy</button>
  <script src="popup.js"></script>
</body>
</html>
