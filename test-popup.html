<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Popup</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 15px;
      width: 280px;
      background-color: #f4f7f6;
      color: #333;
      margin: 0;
      box-sizing: border-box;
    }
    
    button {
      width: 100%;
      padding: 10px 15px;
      margin-bottom: 8px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1em;
      font-weight: bold;
      transition: background-color 0.3s ease, transform 0.2s ease;
    }
    
    button#options {
      background-color: #6c757d;
      color: white;
      font-size: 0.9em;
    }

    button#options:hover {
      background-color: #5a6268;
      transform: translateY(-1px);
    }
    
    .status {
      text-align: center;
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 0.85em;
      display: none;
    }

    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  </style>
</head>
<body>
  <h4>Test More Settings Button</h4>
  
  <button type="button" id="options">More Settings</button>
  
  <div id="status" class="status"></div>
  
  <script>
    // Show status message
    function showStatus(message, isError = false) {
      const statusDiv = document.getElementById('status');
      statusDiv.textContent = message;
      statusDiv.className = `status ${isError ? 'error' : 'success'}`;
      statusDiv.style.display = 'block';
      
      setTimeout(() => {
        statusDiv.style.display = 'none';
      }, 3000);
    }
    
    // Setup event listeners when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
      console.log('DOM loaded, setting up event listeners');
      
      const optionsButton = document.getElementById('options');
      if (optionsButton) {
        console.log('Options button found, adding event listener');
        
        optionsButton.addEventListener('click', () => {
          console.log('Options button clicked');
          showStatus('Opening settings page...');
          
          try {
            // Method 1: Try chrome.runtime.openOptionsPage
            if (chrome.runtime && chrome.runtime.openOptionsPage) {
              console.log('Using chrome.runtime.openOptionsPage');
              chrome.runtime.openOptionsPage(() => {
                if (chrome.runtime.lastError) {
                  console.error('openOptionsPage error:', chrome.runtime.lastError.message);
                  showStatus('Error: ' + chrome.runtime.lastError.message, true);
                  tryFallbackMethod();
                } else {
                  console.log('openOptionsPage succeeded');
                  showStatus('Settings page opened');
                }
              });
            } else {
              console.log('openOptionsPage not available, using fallback');
              tryFallbackMethod();
            }
          } catch (error) {
            console.error('Exception in options button click:', error);
            showStatus('Error: ' + error.message, true);
            tryFallbackMethod();
          }
        });
      } else {
        console.error('Options button not found!');
        showStatus('Options button not found', true);
      }
    });
    
    function tryFallbackMethod() {
      try {
        console.log('Trying fallback method with chrome.tabs.create');
        const optionsUrl = chrome.runtime.getURL('options.html');
        console.log('Options URL:', optionsUrl);
        
        chrome.tabs.create({ url: optionsUrl }, (tab) => {
          if (chrome.runtime.lastError) {
            console.error('tabs.create error:', chrome.runtime.lastError.message);
            showStatus('Fallback error: ' + chrome.runtime.lastError.message, true);
          } else {
            console.log('tabs.create succeeded, tab ID:', tab.id);
            showStatus('Settings page opened (fallback method)');
          }
        });
      } catch (error) {
        console.error('Fallback method failed:', error);
        showStatus('All methods failed: ' + error.message, true);
      }
    }
    
    // Log Chrome APIs availability
    console.log('Chrome runtime available:', !!chrome.runtime);
    console.log('Chrome tabs available:', !!chrome.tabs);
    console.log('openOptionsPage available:', !!(chrome.runtime && chrome.runtime.openOptionsPage));
  </script>
</body>
</html>
