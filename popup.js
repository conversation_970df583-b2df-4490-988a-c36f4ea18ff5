// Load presets and current settings when popup opens
document.addEventListener('DOMContentLoaded', () => {
  loadPresets();
  loadCurrentSettings();
  setupEventListeners();
});

// Load saved presets
function loadPresets() {
  chrome.storage.sync.get(['proxyPresets'], (result) => {
    const presets = result.proxyPresets || {};
    const presetsSelect = document.getElementById('presets');

    // Clear existing options except the first one
    while (presetsSelect.children.length > 2) {
      presetsSelect.removeChild(presetsSelect.lastChild);
    }

    // Add saved presets
    Object.keys(presets).forEach(name => {
      const option = document.createElement('option');
      option.value = name;
      option.textContent = name;
      presetsSelect.appendChild(option);
    });
  });
}

// Load current proxy settings
function loadCurrentSettings() {
  chrome.runtime.sendMessage({ action: "getCurrentProxy" }, (response) => {
    if (response && response.config) {
      const config = response.config;
      if (config.mode === 'fixed_servers' && config.rules && config.rules.singleProxy) {
        const proxy = config.rules.singleProxy;
        document.getElementById('scheme').value = proxy.scheme || '';
        document.getElementById('host').value = proxy.host || '';
        document.getElementById('port').value = proxy.port || '';
      }
    }
  });
}

// Show status message
function showStatus(message, isError = false) {
  const statusDiv = document.getElementById('status');
  statusDiv.textContent = message;
  statusDiv.className = `status ${isError ? 'error' : 'success'}`;
  statusDiv.style.display = 'block';

  setTimeout(() => {
    statusDiv.style.display = 'none';
  }, 3000);
}

// Setup all event listeners
function setupEventListeners() {
  // Handle preset selection
  document.getElementById('presets').addEventListener('change', (e) => {
    const selectedPreset = e.target.value;

    if (selectedPreset === 'direct') {
      // Clear proxy
      chrome.runtime.sendMessage({
        action: "clearProxy"
      }, (response) => {
        showStatus(response.status);
        // Clear manual inputs
        document.getElementById('scheme').value = '';
        document.getElementById('host').value = '';
        document.getElementById('port').value = '';
      });
    } else if (selectedPreset) {
      // Load preset
      chrome.storage.sync.get(['proxyPresets'], (result) => {
        const presets = result.proxyPresets || {};
        const preset = presets[selectedPreset];

        if (preset) {
          document.getElementById('scheme').value = preset.scheme || '';
          document.getElementById('host').value = preset.host || '';
          document.getElementById('port').value = preset.port || '';

          // Apply the preset
          chrome.runtime.sendMessage({
            action: "setProxy",
            scheme: preset.scheme,
            host: preset.host,
            port: preset.port
          }, (response) => {
            showStatus(response.status);
          });
        }
      });
    }
  });

  // Set proxy manually
  document.getElementById('set').addEventListener('click', () => {
    const scheme = document.getElementById('scheme').value;
    const host = document.getElementById('host').value;
    const port = document.getElementById('port').value;

    if (!host || !port) {
      showStatus('Please enter both host and port', true);
      return;
    }

    chrome.runtime.sendMessage({
      action: "setProxy",
      scheme: scheme || 'http',
      host,
      port
    }, (response) => {
      showStatus(response.status);
      // Reset preset selection
      document.getElementById('presets').value = '';
    });
  });

  // Clear proxy
  document.getElementById('clear').addEventListener('click', () => {
    chrome.runtime.sendMessage({
      action: "clearProxy"
    }, (response) => {
      showStatus(response.status);
      // Clear manual inputs and preset selection
      document.getElementById('scheme').value = '';
      document.getElementById('host').value = '';
      document.getElementById('port').value = '';
      document.getElementById('presets').value = 'direct';
    });
  });

  // Open options page
  document.getElementById('options').addEventListener('click', () => {
    try {
      // Try multiple methods to open options page
      if (chrome.runtime.openOptionsPage) {
        chrome.runtime.openOptionsPage((result) => {
          if (chrome.runtime.lastError) {
            console.error('openOptionsPage error:', chrome.runtime.lastError.message);
            // Fallback to tabs.create
            chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
          }
        });
      } else {
        // Fallback method for older Chrome versions
        chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
      }
    } catch (error) {
      console.error('Error opening options page:', error);
      showStatus('Error opening settings page', true);
    }
  });
}
