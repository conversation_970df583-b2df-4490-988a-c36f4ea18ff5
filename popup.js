// Initialize popup when opened
document.addEventListener('DOMContentLoaded', () => {
  loadPresets();
  loadCurrentStatus();
  checkActiveRules();
  setupEventListeners();
});

// Load saved presets into dropdown
function loadPresets() {
  chrome.storage.sync.get(['proxyPresets'], (result) => {
    const presets = result.proxyPresets || {};
    const presetsSelect = document.getElementById('presets');

    // Clear existing options except the first two
    while (presetsSelect.children.length > 2) {
      presetsSelect.removeChild(presetsSelect.lastChild);
    }

    // Add saved presets
    Object.keys(presets).forEach(name => {
      const option = document.createElement('option');
      option.value = name;
      option.textContent = name;
      presetsSelect.appendChild(option);
    });
  });
}

// Load and display current proxy status
function loadCurrentStatus() {
  chrome.runtime.sendMessage({ action: "getCurrentProxy" }, (response) => {
    updateStatusDisplay(response);
  });
}

// Update status display based on current proxy settings
function updateStatusDisplay(response) {
  const currentProxyElement = document.getElementById('current-proxy');
  const presetsSelect = document.getElementById('presets');

  if (response && response.config) {
    const config = response.config;

    if (config.mode === 'fixed_servers' && config.rules && config.rules.singleProxy) {
      const proxy = config.rules.singleProxy;
      currentProxyElement.textContent = `${proxy.host}:${proxy.port}`;
      currentProxyElement.style.color = '#28a745';

      // Try to match with existing presets
      chrome.storage.sync.get(['proxyPresets'], (result) => {
        const presets = result.proxyPresets || {};

        for (const [name, preset] of Object.entries(presets)) {
          if (preset.host === proxy.host &&
              preset.port === proxy.port &&
              preset.scheme === proxy.scheme) {
            presetsSelect.value = name;
            return;
          }
        }
        presetsSelect.value = '';
      });
    } else {
      currentProxyElement.textContent = 'Direct Connection';
      currentProxyElement.style.color = '#6c757d';
      presetsSelect.value = 'direct';
    }
  } else {
    currentProxyElement.textContent = 'Direct Connection';
    currentProxyElement.style.color = '#6c757d';
    presetsSelect.value = 'direct';
  }
}

// Check for active auto-switch rules
function checkActiveRules() {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    if (tabs.length === 0) return;

    const currentUrl = tabs[0].url;
    if (!currentUrl) return;

    chrome.storage.sync.get(['autoSwitchRules'], (result) => {
      const rules = result.autoSwitchRules || [];
      const activeRuleElement = document.getElementById('active-rule');
      const activeRuleTextElement = document.getElementById('active-rule-text');

      for (const rule of rules) {
        if (rule.enabled !== false && matchesPattern(currentUrl, rule.pattern)) {
          activeRuleElement.style.display = 'flex';
          activeRuleTextElement.textContent = rule.pattern;
          activeRuleTextElement.title = `Pattern: ${rule.pattern}\nProxy: ${rule.preset}`;
          return;
        }
      }

      activeRuleElement.style.display = 'none';
    });
  });
}

// Simple pattern matching function
function matchesPattern(url, pattern) {
  try {
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*');
    const regex = new RegExp(regexPattern, 'i');
    return regex.test(url);
  } catch (e) {
    return false;
  }
}

// Show status message
function showStatus(message, isError = false) {
  const statusDiv = document.getElementById('status');
  statusDiv.textContent = message;
  statusDiv.className = `status ${isError ? 'error' : 'success'}`;
  statusDiv.style.display = 'block';

  setTimeout(() => {
    statusDiv.style.display = 'none';
  }, 3000);
}

// Setup event listeners for quick switching
function setupEventListeners() {
  // Handle preset selection for quick switching
  document.getElementById('presets').addEventListener('change', (e) => {
    const selectedPreset = e.target.value;

    if (selectedPreset === 'direct') {
      // Clear proxy
      chrome.runtime.sendMessage({
        action: "clearProxy"
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('Error clearing proxy:', chrome.runtime.lastError.message);
          showStatus('Error clearing proxy', true);
        } else {
          console.log('Direct connection activated:', response);
          showStatus(response.status);
          loadCurrentStatus(); // Refresh status display
        }
      });
    } else if (selectedPreset) {
      // Apply preset
      chrome.storage.sync.get(['proxyPresets'], (result) => {
        const presets = result.proxyPresets || {};
        const preset = presets[selectedPreset];

        if (preset) {
          chrome.runtime.sendMessage({
            action: "setProxy",
            scheme: preset.scheme,
            host: preset.host,
            port: preset.port
          }, (response) => {
            if (chrome.runtime.lastError) {
              console.error('Error setting proxy:', chrome.runtime.lastError.message);
              showStatus('Error setting proxy', true);
            } else {
              console.log(`Preset "${selectedPreset}" activated:`, response);
              showStatus(response.status);
              loadCurrentStatus(); // Refresh status display
            }
          });
        }
      });
    }
  });

  // Direct connection button
  document.getElementById('clear').addEventListener('click', () => {
    chrome.runtime.sendMessage({
      action: "clearProxy"
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Error clearing proxy:', chrome.runtime.lastError.message);
        showStatus('Error clearing proxy', true);
      } else {
        console.log('Proxy cleared:', response);
        showStatus(response.status);
        loadCurrentStatus(); // Refresh status display
        document.getElementById('presets').value = 'direct';
      }
    });
  });

  // Open settings page
  document.getElementById('options').addEventListener('click', () => {
    try {
      if (chrome.runtime.openOptionsPage) {
        chrome.runtime.openOptionsPage((result) => {
          if (chrome.runtime.lastError) {
            console.error('openOptionsPage error:', chrome.runtime.lastError.message);
            chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
          }
        });
      } else {
        chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
      }
      window.close(); // Close popup after opening settings
    } catch (error) {
      console.error('Error opening options page:', error);
      showStatus('Error opening settings page', true);
    }
  });
}