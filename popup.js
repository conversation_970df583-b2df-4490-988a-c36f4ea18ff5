// Load presets and current settings when popup opens
document.addEventListener('DOMContentLoaded', () => {
  loadCustomPresets();
  loadCurrentSettings();
  setupEventListeners();

  // Add sample preset if none exist (for demo purposes)
  chrome.storage.sync.get(['proxyPresets'], (result) => {
    const presets = result.proxyPresets || {};
    if (Object.keys(presets).length === 0) {
      const samplePresets = {
        'V2ray': {
          scheme: 'socks5',
          host: '127.0.0.1',
          port: 1080
        }
      };
      chrome.storage.sync.set({ proxyPresets: samplePresets }, () => {
        loadCustomPresets(); // Reload to show the sample preset
      });
    }
  });
});

// Load saved custom presets
function loadCustomPresets() {
  chrome.storage.sync.get(['proxyPresets'], (result) => {
    const presets = result.proxyPresets || {};
    const customPresetsContainer = document.getElementById('custom-presets');

    // Clear existing custom presets
    customPresetsContainer.innerHTML = '';

    // Add saved presets
    Object.keys(presets).forEach(name => {
      const preset = presets[name];
      const presetItem = document.createElement('div');
      presetItem.className = 'proxy-item custom-preset';
      presetItem.setAttribute('data-preset', name);
      presetItem.innerHTML = `
        <div class="proxy-icon icon-custom">🔗</div>
        <div class="proxy-name">${name}</div>
        <div class="proxy-status">${preset.scheme}://${preset.host}:${preset.port}</div>
      `;
      customPresetsContainer.appendChild(presetItem);
    });
  });
}

// Load current proxy settings and update UI
function loadCurrentSettings() {
  chrome.runtime.sendMessage({ action: "getCurrentProxy" }, (response) => {
    updateActiveProxyItem(response);
  });
}

// Update active proxy item based on current settings
function updateActiveProxyItem(response) {
  // Remove active class from all items
  document.querySelectorAll('.proxy-item').forEach(item => {
    item.classList.remove('active');
  });

  if (response && response.config) {
    const config = response.config;

    if (config.mode === 'system') {
      document.getElementById('system-item').classList.add('active');
    } else if (config.mode === 'fixed_servers' && config.rules && config.rules.singleProxy) {
      // Check if it matches any custom preset
      const proxy = config.rules.singleProxy;
      let foundMatch = false;

      chrome.storage.sync.get(['proxyPresets'], (result) => {
        const presets = result.proxyPresets || {};

        for (const [name, preset] of Object.entries(presets)) {
          if (preset.host === proxy.host && preset.port === proxy.port && preset.scheme === proxy.scheme) {
            const presetItem = document.querySelector(`[data-preset="${name}"]`);
            if (presetItem) {
              presetItem.classList.add('active');
              foundMatch = true;
            }
            break;
          }
        }

        // If no preset match found, it might be a manual configuration
        if (!foundMatch) {
          // Could add a "Custom" indicator here
        }
      });
    } else {
      // Direct connection or other modes
      document.getElementById('direct-item').classList.add('active');
    }
  } else {
    // Default to direct connection
    document.getElementById('direct-item').classList.add('active');
  }
}

// Setup all event listeners
function setupEventListeners() {
  // Handle proxy item clicks
  document.addEventListener('click', (e) => {
    const proxyItem = e.target.closest('.proxy-item');
    if (proxyItem) {
      const preset = proxyItem.getAttribute('data-preset');
      handleProxySelection(preset);
    }
  });

  // Open options page
  document.getElementById('options').addEventListener('click', () => {
    try {
      if (chrome.runtime.openOptionsPage) {
        chrome.runtime.openOptionsPage((result) => {
          if (chrome.runtime.lastError) {
            console.error('openOptionsPage error:', chrome.runtime.lastError.message);
            chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
          }
        });
      } else {
        chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
      }
    } catch (error) {
      console.error('Error opening options page:', error);
    }
  });
}

// Handle proxy selection
function handleProxySelection(preset) {
  if (preset === 'direct') {
    // Clear proxy - direct connection
    chrome.runtime.sendMessage({
      action: "clearProxy"
    }, (response) => {
      console.log('Direct connection activated');
      loadCurrentSettings(); // Refresh UI
    });
  } else if (preset === 'system') {
    // Use system proxy settings
    chrome.runtime.sendMessage({
      action: "setSystemProxy"
    }, (response) => {
      console.log('System proxy activated');
      loadCurrentSettings(); // Refresh UI
    });
  } else if (preset === 'auto') {
    // Auto switch mode (placeholder for future implementation)
    console.log('Auto switch mode - not implemented yet');
  } else {
    // Custom preset
    chrome.storage.sync.get(['proxyPresets'], (result) => {
      const presets = result.proxyPresets || {};
      const selectedPreset = presets[preset];

      if (selectedPreset) {
        chrome.runtime.sendMessage({
          action: "setProxy",
          scheme: selectedPreset.scheme,
          host: selectedPreset.host,
          port: selectedPreset.port
        }, (response) => {
          console.log(`Preset "${preset}" activated`);
          loadCurrentSettings(); // Refresh UI
        });
      }
    });
  }
}
