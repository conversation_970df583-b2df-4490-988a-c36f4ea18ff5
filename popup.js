document.getElementById('set').addEventListener('click', () => {
  const scheme = document.getElementById('scheme').value;
  const host = document.getElementById('host').value;
  const port = document.getElementById('port').value;

  chrome.runtime.sendMessage({
    action: "setProxy",
    scheme,
    host,
    port
  }, (response) => {
    alert(response.status);
  });
});

document.getElementById('clear').addEventListener('click', () => {
  chrome.runtime.sendMessage({
    action: "clearProxy"
  }, (response) => {
    alert(response.status);
  });
});
