# 代理切换器扩展测试指南

## 安装扩展

1. 打开Chrome浏览器
2. 进入 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹

## 测试功能

### 1. 基本代理设置（popup界面）

**测试步骤：**
1. 点击扩展图标打开popup
2. 在"Manual Configuration"部分输入：
   - Scheme: `http`
   - Host: `127.0.0.1`
   - Port: `8080`
3. 点击"Set Proxy"按钮
4. 验证状态消息显示"Proxy set successfully"

**预期结果：**
- 界面应该显示成功消息
- Chrome的代理设置应该被更新

### 2. 清除代理

**测试步骤：**
1. 在popup界面点击"Clear Proxy"按钮
2. 验证状态消息

**预期结果：**
- 显示"Proxy cleared successfully"
- Chrome恢复直接连接

### 3. 预设功能测试

**测试步骤：**
1. 点击"More Settings"按钮打开配置页面
2. 在"Proxy Presets"标签页中：
   - 输入预设名称：`Test Proxy`
   - 选择Scheme：`http`
   - 输入Host：`proxy.example.com`
   - 输入Port：`8080`
3. 点击"Save Preset"
4. 返回popup界面
5. 在"Quick Presets"下拉菜单中选择刚创建的预设

**预期结果：**
- 预设应该被保存并显示在列表中
- 在popup中选择预设应该自动填充配置并应用

### 4. 高级设置测试

**测试步骤：**
1. 在配置页面切换到"Advanced Settings"标签
2. 在"Bypass List"中添加：
   ```
   localhost
   127.0.0.1
   *.local
   example.com
   ```
3. 勾选"Bypass proxy for local addresses"
4. 点击"Save Advanced Settings"

**预期结果：**
- 设置应该被保存
- 后续的代理配置应该包含这些绕过规则

### 5. 自动切换规则测试

**测试步骤：**
1. 切换到"Auto Switch Rules"标签
2. 添加规则：
   - URL Pattern: `*.google.com`
   - Proxy Preset: `Direct Connection`
3. 点击"Add Rule"

**预期结果：**
- 规则应该被添加到列表中
- （注意：自动切换功能需要访问相应网站才能测试）

### 6. 备份和恢复测试

**测试步骤：**
1. 切换到"Backup & Restore"标签
2. 点击"Export Configuration"
3. 验证下载的JSON文件
4. 点击"Reset All Settings"确认重置
5. 使用"Import Configuration"导入之前的配置

**预期结果：**
- 应该能够成功导出配置文件
- 重置后所有设置应该被清除
- 导入后设置应该被恢复

## 常见问题排查

### 扩展无法加载
- 检查manifest.json语法
- 确保所有文件都在正确位置

### 代理设置不生效
- 检查Chrome的代理权限
- 验证代理服务器地址和端口

### 配置页面无法打开
- 检查options.html和options.js是否正确加载
- 查看浏览器控制台错误信息

### 预设无法保存
- 检查Chrome存储权限
- 验证输入的数据格式

## 功能特性总结

✅ **已实现的功能：**
- 基本代理设置和清除
- 代理预设管理
- 高级绕过规则配置
- 自动切换规则（基础框架）
- 配置导入/导出
- 现代化的用户界面
- 双层界面设计（简单+详细）

🔄 **可扩展的功能：**
- PAC脚本支持
- 代理链配置
- 流量统计
- 代理测试和延迟检测
- 更复杂的自动切换规则
- 主题定制

## 技术架构

- **popup.html/js**: 简单的快速设置界面
- **options.html/js**: 详细的配置管理界面
- **background.js**: 后台服务，处理代理设置和规则
- **manifest.json**: 扩展配置和权限声明
- **Chrome Storage API**: 配置数据持久化
- **Chrome Proxy API**: 代理设置管理
